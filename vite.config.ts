import * as fs from 'node:fs';
import * as path from 'node:path';

import { sentryVitePlugin } from '@sentry/vite-plugin';
import { TanStackRouterVite } from '@tanstack/router-vite-plugin';
import react from '@vitejs/plugin-react-swc';
import TurboConsole from 'unplugin-turbo-console/vite';
import { defineConfig, loadEnv } from 'vite';
import svgr from 'vite-plugin-svgr';
import webfontDownload from 'vite-plugin-webfont-dl';
const port = 3000;

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  const plugins = [
    react(),
    TanStackRouterVite(),
    svgr(),
    TurboConsole({
      port,
    }),
    webfontDownload([
      'https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap',
    ]),
  ];

  if (env?.NODE_ENV === 'production') {
    plugins.push(
      sentryVitePlugin({
        authToken: env.SENTRY_AUTH_TOKEN,
        org: 'esto-as',
        project: 'customer-profile-v3',
        release: {
          inject: false,
        },
      }),
    );
  }

  return {
    build: {
      sourcemap: env?.NODE_ENV === 'development',
      rollupOptions: {
        input: {
          main: './index.html',
          'firebase-messaging-sw': './src/firebase-messaging-sw.js',
        },
        output: {
          entryFileNames: (chunkInfo) => {
            return chunkInfo.name === 'firebase-messaging-sw'
              ? '[name].js' // Output service worker in root
              : 'assets/[name]-[hash].js'; // Others in `assets/`
          },

          manualChunks: (id) => {
            if (id.includes('rudderstack/analytics-js'))
              return 'vendor-rudderstack';
            if (id.includes('zod')) return 'vendor-zod';
            if (id.includes('i18next')) return 'vendor-i18next';
          },
        },
      },
    },
    plugins,
    resolve: {
      alias: {
        '/': path.resolve(__dirname, '.'),
        '@': path.resolve(__dirname, './src'),
        '@config': path.resolve(__dirname, './src/shared/config'),
        '@components': path.resolve(__dirname, './src/shared/components'),
        '@icons': path.resolve(__dirname, './src/shared/assets/icons'),
        '@pages': path.resolve(__dirname, './src/pages'),
        '@routes': path.resolve(__dirname, './src/routes'),
        '@hooks': path.resolve(__dirname, './src/shared/hooks'),
        '@features': path.resolve(__dirname, './src/features'),
        '@app': path.resolve(__dirname, './src/app'),
        '@entities': path.resolve(__dirname, './src/entities'),
        '@widgets': path.resolve(__dirname, './src/widgets'),
        '@lib': path.resolve(__dirname, './src/shared/lib'),
        '@utils': path.resolve(__dirname, './src/shared/utils'),
      },
    },
    define: {
      'process.env': env,
    },
    server: {
      host: '0.0.0.0', // Allow external connections
      port: 3000,
    },
    test: {
      include: ['./**/*.test.ts', './**/*.test.tsx'],
      globals: true,
      environment: 'jsdom',
      env,
      setupFiles: './tests/setup.tsx',
    },
    css: {
      modules: {
        localsConvention: 'camelCaseOnly',
      },
    },
  };
});
