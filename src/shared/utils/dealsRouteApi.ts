import { APP_COUNTRY } from '@config/envs';
import { getRouteApi } from '@tanstack/react-router';

import { SupportedCountries } from '@/shared/types';

export const getDealsRouteApi = () => {
  switch (APP_COUNTRY) {
    case SupportedCountries.EE:
      return getRouteApi('/_protected/_main/pakkumised');
    case SupportedCountries.LV:
      return getRouteApi('/_protected/_main/akcijas');
    case SupportedCountries.LT:
      return getRouteApi('/_protected/_main/akcijos');
    default:
      return getRouteApi('/_protected/_main/deals');
  }
};

export const getDealDetailRouteApi = () => {
  switch (APP_COUNTRY) {
    case SupportedCountries.EE:
      return getRouteApi('/_protected/_main/pakkumised/$dealId');
    case SupportedCountries.LV:
      return getRouteApi('/_protected/_main/akcijas/$dealId');
    case SupportedCountries.LT:
      return getRouteApi('/_protected/_main/akcijos/$dealId');
    default:
      return getRouteApi('/_protected/_main/deals/$dealId');
  }
};
