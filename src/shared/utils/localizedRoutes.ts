import { APP_COUNTRY } from '@config/envs';

import { SupportedCountries } from '@/shared/types';

export const LOCALIZED_ROUTES = {
  deals: {
    [SupportedCountries.EE]: '/pakkumised',
    [SupportedCountries.LV]: '/akcijas',
    [SupportedCountries.LT]: '/akcijos',
  },
} as const;

export const LOCALIZED_ROUTES_REVERSE = {
  '/pakkumised': 'deals',
  '/akcijas': 'deals',
  '/akcijos': 'deals',
} as const;

export const getLocalizedPath = (
  routeName: keyof typeof LOCALIZED_ROUTES,
): string => {
  const localizedPaths = LOCALIZED_ROUTES[routeName];

  if (!localizedPaths) {
    throw new Error(`No localized paths found for route: ${routeName}`);
  }

  const localizedPath = localizedPaths[APP_COUNTRY];

  if (!localizedPath) {
    throw new Error(
      `No localized path found for route: ${routeName} and country: ${APP_COUNTRY}`,
    );
  }

  return localizedPath;
};

export const getRouteNameFromLocalizedPath = (
  localizedPath: string,
): string | null => {
  return (
    LOCALIZED_ROUTES_REVERSE[
      localizedPath as keyof typeof LOCALIZED_ROUTES_REVERSE
    ] || null
  );
};

export const isLocalizedRoute = (path: string): boolean => {
  return path in LOCALIZED_ROUTES_REVERSE;
};

export const getAllLocalizedPaths = (
  routeName: keyof typeof LOCALIZED_ROUTES,
): string[] => {
  const localizedPaths = LOCALIZED_ROUTES[routeName];

  if (!localizedPaths) {
    return [];
  }

  return Object.values(localizedPaths);
};

export const convertLegacyPathToLocalized = (legacyPath: string): string => {
  // Handle deals path conversion
  if (legacyPath === '/deals' || legacyPath.startsWith('/deals/')) {
    const localizedDealsPath = getLocalizedPath('deals');
    return legacyPath.replace('/deals', localizedDealsPath);
  }

  return legacyPath;
};
