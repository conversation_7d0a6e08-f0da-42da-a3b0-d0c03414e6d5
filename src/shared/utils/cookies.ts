import Cookies from 'js-cookie';

import { EPARAKSTS_ORIGINAL_URI_COOKIE } from '@/shared/config/cookies';

const getParentDomain = (): string | undefined => {
  const hostname = window.location.hostname;

  if (hostname === 'localhost' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return undefined;
  }

  const parts = hostname.split('.');
  if (parts.length >= 2) {
    return `.${parts.slice(-2).join('.')}`;
  }

  return undefined;
};

export const setCookie = (
  name: string,
  value: string,
  options: {
    maxAge?: number;
    path?: string;
    domain?: string;
    sameSite?: 'Strict' | 'Lax' | 'None';
    secure?: boolean;
  } = {},
): void => {
  const {
    maxAge = 3600,
    path = '/',
    domain = getParentDomain(),
    sameSite = 'Lax',
    secure = false,
  } = options;

  const expires = maxAge ? maxAge / (24 * 60 * 60) : undefined;

  Cookies.set(name, value, {
    expires,
    path,
    domain,
    sameSite,
    secure,
  });
};

export const getCookie = (name: string): string | null => {
  return Cookies.get(name) ?? null;
};

export const removeCookie = (
  name: string,
  options: { path?: string; domain?: string } = {},
): void => {
  const { path = '/', domain = getParentDomain() } = options;

  Cookies.remove(name, {
    path,
    domain,
  });
};

export const storeEParakstsOriginalUri = (): void => {
  const currentUri = window.location.href;
  const parentDomain = getParentDomain();

  setCookie(EPARAKSTS_ORIGINAL_URI_COOKIE, currentUri, {
    path: '/',
    domain: parentDomain,
    sameSite: 'Lax',
  });
};

export const getAndRemoveEParakstsOriginalUri = (): string | null => {
  const originalUri = getCookie(EPARAKSTS_ORIGINAL_URI_COOKIE);

  if (originalUri) {
    const parentDomain = getParentDomain();

    removeCookie(EPARAKSTS_ORIGINAL_URI_COOKIE, {
      path: '/',
      domain: parentDomain,
    });
  }

  return originalUri;
};
