import { useCallback, useEffect, useState } from 'react';

import { useIntersectionObserver } from './use-intersection-observer';

export interface UseInfiniteScrollOptions {
  enabled?: boolean;
  rootMargin?: string;
  threshold?: number;
  hasNextPage?: boolean;
  isFetching?: boolean;
}

export interface UseInfiniteScrollResult {
  triggerRef: (node: HTMLElement | null) => void;
  isIntersecting: boolean;
}

export const useInfiniteScroll = (
  onLoadMore: () => void,
  options: UseInfiniteScrollOptions = {},
): UseInfiniteScrollResult => {
  const {
    enabled = true,
    rootMargin = '100px',
    threshold = 0.1,
    hasNextPage = true,
    isFetching = false,
  } = options;

  const [hasTriggered, setHasTriggered] = useState(false);

  const { ref: triggerRef, isIntersecting } = useIntersectionObserver({
    rootMargin,
    threshold,
  });

  const handleLoadMore = useCallback(() => {
    if (
      enabled &&
      isIntersecting &&
      hasNextPage &&
      !isFetching &&
      !hasTriggered
    ) {
      setHasTriggered(true);
      onLoadMore();

      setTimeout(() => {
        setHasTriggered(false);
      }, 500);
    }
  }, [
    enabled,
    isIntersecting,
    hasNextPage,
    isFetching,
    hasTriggered,
    onLoadMore,
  ]);

  useEffect(() => {
    handleLoadMore();
  }, [handleLoadMore]);

  return {
    triggerRef,
    isIntersecting,
  };
};
