export const LOCIZE_CREDIT_LINE_BALANCE_WIDGET_KEYS = {
  noSignedHeading: 'no-signed.heading',
  noSignedDescription: 'no-signed.description',
  noSignedDescriptionLink: 'no-signed.description-link',
  noSignedFormAmount: 'no-signed.form.amount',
  noSignedFormMonthlyPayment: 'no-signed.form.monthly-payment',
  noSignedFormButton: 'no-signed.form.button',
  noSignedFormCaption: 'no-signed.form.caption',
  noSignedDeletedCaTitle: 'no-signed.deleted-ca.title',
  noSignedDeletedCaDescription: 'no-signed.deleted-ca.description',
  noSignedDeletedCaSupportButton: 'no-signed.deleted-ca.support-button',
  noSignedTerminatedCaTitle: 'no-signed.terminated-ca.title',
  noSignedTerminatedCaDescription: 'no-signed.terminated-ca.description',
  noSignedSecondaryDisclaimer: 'no-signed-secondary.disclaimer',
  noSignedSecondaryPromotion1: 'no-signed-secondary.promotion-1',
  noSignedSecondaryPromotion2: 'no-signed-secondary.promotion-2',
  noSignedSecondaryApplyButton: 'no-signed-secondary.apply-button',
  noSignedSecondaryMoreButton: 'no-signed-secondary.more-button',
  limitIncreaseLabel: 'limit-increase.label',
  creditAccountModificationLabel: 'credit-account-modification.label',
  creditAccountModificationActionText:
    'credit-account-modification.action-text',
  limitIncreaseActionText: 'limit-increase.action-text',
  signedFirstWithdrawalTitle: 'signed.first-withdrawal.title',
  withdrawButton: 'withdraw-button',
  noLimitTooltip: 'no-limit.tooltip',
  lateInvoiceTooltip: 'late-invoice.tooltip',
  title: 'title',
  signedCaAmountMinLabel: 'signed-ca.amount.min-label',
} as const;
