import {
  Dialog as DialogContainer,
  DialogContent,
} from '@components/ui/app-dialog';
import type * as DialogPrimitive from '@radix-ui/react-dialog';
import type { DialogProps } from '@radix-ui/react-dialog';
import {
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
  type ReactNode,
} from 'react';

export const AppDialog = forwardRef<
  ElementRef<typeof DialogPrimitive.Content>,
  ComponentPropsWithoutRef<typeof DialogPrimitive.Content> &
    DialogProps & {
      title?: ReactNode;
      hideCloseButton?: boolean;
      customCloseButton?: ReactNode;
    }
>(
  (
    {
      open,
      onOpenChange,
      children,
      hideCloseButton,
      customCloseButton,
      title,
      ...props
    },
    ref,
  ) => (
    <DialogContainer open={open} onOpenChange={onOpenChange}>
      <DialogContent
        title={title}
        hideCloseButton={hideCloseButton}
        customCloseButton={customCloseButton}
        ref={ref}
        {...props}
      >
        {children}
      </DialogContent>
    </DialogContainer>
  ),
);

AppDialog.displayName = 'AppDialog';
