import { Button } from '@components/ui/button';
import InfiniteScroll from '@components/ui/InfiniteScroll';
import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';
import useEmblaCarousel from 'embla-carousel-react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

type CardsCarouselProps = {
  title: React.ReactNode;
  actionBtn?: React.ReactNode;
  children: React.ReactNode;
  hasNextPage?: boolean;
  fetchNextPage?: () => void;
  isFetchingNextPage?: boolean;
  className?: string;
};

export const CardsCarousel = ({
  title,
  actionBtn,
  children,
  hasNextPage,
  fetchNextPage,
  isFetchingNextPage,
  className,
}: CardsCarouselProps) => {
  const isMobileView = useIsMobileView();

  // Performance optimizations for mobile devices including iOS Safari
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: 'start',
    dragFree: true,
    containScroll: 'trimSnaps',
    // Critical optimizations for mobile performance
    dragThreshold: 5, // Reduced from default 10 for better responsiveness
    skipSnaps: false,
    // iOS Safari fix: Remove complex watchDrag logic that interferes with touch events
    // The original cancelable check was causing issues on iOS Safari
    watchDrag: true,
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  // Use ref to prevent unnecessary re-renders during scroll
  const isScrollingRef = useRef(false);

  // Optimized fetch next page callback
  const handleFetchNextPage = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && fetchNextPage) {
      console.log('🚀 InfiniteScroll triggered - fetching next page');
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Throttled update scroll buttons for better performance
  const updateScrollButtons = useCallback(() => {
    if (!emblaApi || isScrollingRef.current) return;

    // Use requestAnimationFrame for smooth updates
    requestAnimationFrame(() => {
      if (emblaApi) {
        setCanScrollPrev(emblaApi.canScrollPrev());
        setCanScrollNext(emblaApi.canScrollNext());
      }
    });
  }, [emblaApi]);

  // Optimized navigation functions with scroll state tracking
  const scrollPrev = useCallback(() => {
    if (!emblaApi || isScrollingRef.current) return;
    isScrollingRef.current = true;
    emblaApi.scrollPrev();

    // Reset scroll state after animation
    setTimeout(() => {
      isScrollingRef.current = false;
    }, 100);
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (!emblaApi || isScrollingRef.current) return;
    isScrollingRef.current = true;
    emblaApi.scrollNext();

    // Reset scroll state after animation
    setTimeout(() => {
      isScrollingRef.current = false;
    }, 100);
  }, [emblaApi]);

  // Optimized event listeners with performance improvements
  useEffect(() => {
    if (!emblaApi) return;

    // Use more efficient events for mobile performance
    const onSelect = () => {
      updateScrollButtons();
    };

    const onSettle = () => {
      isScrollingRef.current = false;
      updateScrollButtons();
    };

    // Listen to both select and settle for better state management
    emblaApi.on('select', onSelect);
    emblaApi.on('settle', onSettle);

    // Initial state
    updateScrollButtons();

    return () => {
      emblaApi.off('select', onSelect);
      emblaApi.off('settle', onSettle);
    };
  }, [emblaApi, updateScrollButtons]);

  // iOS Safari specific fix: Add proper touch event handling
  useEffect(() => {
    if (!emblaApi) return;

    const emblaContainer = emblaApi.containerNode();
    if (!emblaContainer) return;

    // Detect iOS Safari
    const isIOSSafari =
      /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);

    if (isIOSSafari) {
      let startX = 0;
      let startY = 0;

      const handleTouchStartCapture = (e: TouchEvent) => {
        // Store initial touch position for move calculation
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
      };

      const handleTouchMove = (e: TouchEvent) => {
        // Allow horizontal scrolling, prevent vertical scrolling
        const deltaX = Math.abs(e.touches[0].clientX - startX);
        const deltaY = Math.abs(e.touches[0].clientY - startY);

        if (deltaX > deltaY) {
          e.preventDefault();
        }
      };

      // Add event listeners with proper options for iOS Safari
      emblaContainer.addEventListener('touchstart', handleTouchStartCapture, {
        passive: true,
        capture: true,
      });
      emblaContainer.addEventListener('touchmove', handleTouchMove, {
        passive: false,
      });

      return () => {
        emblaContainer.removeEventListener(
          'touchstart',
          handleTouchStartCapture,
        );
        emblaContainer.removeEventListener('touchmove', handleTouchMove);
      };
    }
  }, [emblaApi]);

  return (
    <div className={cn('w-full overflow-hidden', className)}>
      <div className="mb-6 md:mb-2 grid grid-flow-col items-center px-6 md:px-0">
        {title}

        <div className="relative ml-auto h-8">
          {actionBtn}

          {!isMobileView && (
            <>
              <Button
                variant="outline"
                size="small"
                onClick={scrollPrev}
                disabled={!canScrollPrev}
                className={cn(
                  actionBtn ? '!-left-[5.7rem]' : '!-left-[4.7rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50 h-8 w-8',
                )}
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Previous slide</span>
              </Button>
              <Button
                variant="outline"
                size="small"
                onClick={scrollNext}
                disabled={!canScrollNext}
                className={cn(
                  actionBtn ? '!-left-[3rem]' : '!-left-[2rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50 h-8 w-8',
                )}
              >
                <ArrowRight className="h-4 w-4" />
                <span className="sr-only">Next slide</span>
              </Button>
            </>
          )}
        </div>
      </div>

      <div
        className="overflow-hidden pl-6 md:pl-0"
        ref={emblaRef}
        style={{
          // Hardware acceleration for smoother scrolling on mobile
          transform: 'translateZ(0)',
          willChange: 'transform',
          // iOS Safari fix: Use manipulation instead of pan-x for better touch handling
          touchAction: 'manipulation',
          // Prevent iOS Safari from interfering with touch events
          WebkitTouchCallout: 'none',
          WebkitUserSelect: 'none',
          userSelect: 'none',
          // Ensure proper cursor for desktop
          cursor: 'grab',
        }}
      >
        <div
          className="flex -ml-6 mr-5 py-4"
          style={{
            // Additional hardware acceleration
            transform: 'translateZ(0)',
            backfaceVisibility: 'hidden',
            // iOS Safari specific optimizations
            WebkitUserSelect: 'none',
            userSelect: 'none',
            // Prevent iOS bounce scrolling interference
            WebkitOverflowScrolling: 'touch',
            // Ensure proper touch handling
            touchAction: 'manipulation',
          }}
        >
          <InfiniteScroll
            isLoading={!!isFetchingNextPage}
            hasMore={!!hasNextPage}
            next={handleFetchNextPage}
            threshold={0.8}
            rootMargin="100px"
          >
            {children}

            {hasNextPage && (
              <div className="flex-shrink-0 basis-auto pl-6 flex items-center justify-center min-w-[64px]">
                {isFetchingNextPage && (
                  <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                )}
              </div>
            )}
          </InfiniteScroll>
        </div>
      </div>
    </div>
  );
};
