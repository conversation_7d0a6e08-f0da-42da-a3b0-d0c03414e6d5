import { Slot } from '@radix-ui/react-slot';
import * as React from 'react';

import { cn } from '@/shared/utils/tailwind';

export type DefaultLinkProps = {
  asChild?: boolean;
  disabled?: boolean;
} & React.AnchorHTMLAttributes<HTMLAnchorElement>;

const DefaultLink = React.forwardRef<HTMLAnchorElement, DefaultLinkProps>(
  (
    {
      className,
      asChild = false,
      children,
      href,
      target,
      rel,
      disabled,
      ...props
    },
    ref,
  ) => {
    const Comp = asChild ? Slot : 'a';

    // Automatically add rel="noopener noreferrer" for external links
    const isExternalLink = target === '_blank';
    const linkRel = isExternalLink
      ? rel
        ? `${rel} noopener noreferrer`
        : 'noopener noreferrer'
      : rel;

    return (
      <Comp
        {...props}
        ref={ref}
        className={cn(className, disabled && 'pointer-events-none')}
        href={href}
        target={target}
        rel={linkRel}
      >
        {children}
      </Comp>
    );
  },
);
DefaultLink.displayName = 'DefaultLink';

export { DefaultLink };
