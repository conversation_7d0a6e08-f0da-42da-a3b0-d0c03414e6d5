import { Helmet } from './Helmet';

export type SeoData = Nullish<{
  title: string;
  description: string;
  keywords: string;
}>;

type SeoHelmetProps = {
  seoData: Nullish<SeoData>;
};

export const SeoHelmet = ({ seoData }: SeoHelmetProps) => {
  if (!seoData) {
    return null;
  }

  return (
    <Helmet
      title={seoData.title}
      meta={[
        {
          name: 'description',
          content: seoData.description,
        },
        {
          name: 'keywords',
          content: seoData.keywords,
        },
      ]}
      isIndexable={true}
    />
  );
};
