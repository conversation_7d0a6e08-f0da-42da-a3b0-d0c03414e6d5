import { cn } from '@/shared/utils/tailwind';

interface BurgerButtonProps {
  /** Whether the burger menu is open (controls animation state) */
  isOpen: boolean;
  /** Click handler for the button */
  onClick: () => void;
  /** Optional aria label for accessibility */
  ariaLabel?: string;
  /** Optional className for the button container */
  className?: string;
  /** Optional className for the hamburger lines */
  lineClassName?: string;
  /** Optional size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Optional color variant */
  color?: 'black' | 'white' | 'gray';
}

const sizeClasses = {
  sm: 'size-6',
  md: 'size-7',
  lg: 'size-8',
};

const lineSizeClasses = {
  sm: 'h-0.5 w-5',
  md: 'h-1 w-7',
  lg: 'h-1 w-8',
};

const colorClasses = {
  black: 'bg-black',
  white: 'bg-white',
  gray: 'bg-gray-600',
};

export const BurgerButton = ({
  isOpen,
  onClick,
  ariaLabel = 'Toggle menu',
  className,
  lineClassName,
  size = 'md',
  color = 'black',
}: BurgerButtonProps) => {
  const genericHamburgerLine = cn(
    lineSizeClasses[size],
    colorClasses[color],
    'transition ease transform duration-300',
    lineClassName,
  );

  return (
    <button
      aria-label={ariaLabel}
      className={cn(
        'flex flex-col items-center justify-center gap-1',
        sizeClasses[size],
        className,
      )}
      onClick={onClick}
      type="button"
    >
      <div
        className={cn(
          genericHamburgerLine,
          isOpen && 'translate-y-2 rotate-45',
        )}
      />
      <div className={cn(genericHamburgerLine, isOpen && 'opacity-0')} />
      <div
        className={cn(
          genericHamburgerLine,
          isOpen && '-translate-y-2 -rotate-45',
        )}
      />
    </button>
  );
};
