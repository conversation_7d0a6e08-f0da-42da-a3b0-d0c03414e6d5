import { ROUTE_NAMES } from '@config/routes';
import { useToast } from '@hooks/system';
import { useNavigate } from '@tanstack/react-router';

export const useGracePeriodSigningHandlers = () => {
  const navigate = useNavigate();
  const { showErrorMessage } = useToast();

  const handleSigningSuccess = () => {
    navigate({
      to: ROUTE_NAMES.gracePeriodSuccess,
      replace: true,
    });
  };

  const handleSigningReject = () => {
    navigate({
      to: ROUTE_NAMES.gracePeriodReject,
      replace: true,
    });
  };

  const handleSigningError = (error: unknown) => {
    if (Array.isArray(error)) {
      showErrorMessage(error?.[0]?.message);
      return;
    }

    showErrorMessage();
  };

  return {
    handleSigningSuccess,
    handleSigningReject,
    handleSigningError,
  };
};
