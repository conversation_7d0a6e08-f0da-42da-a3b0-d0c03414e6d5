import { LOCIZE_DEALS_KEYS } from '@config/locize';
import {
  Baby,
  Bike,
  Building2,
  Car,
  Dog,
  Droplets,
  Dumbbell,
  Euro,
  Gem,
  Gift,
  Guitar,
  Hammer,
  Heart,
  HelpCircle,
  type LucideIcon,
  MonitorSmartphone,
  Plane,
  Shirt,
  Sofa,
  Tag,
  Tent,
} from 'lucide-react';

import { DealCategoryName } from './types';

export const DEAL_CATEGORY_ICONS: Record<DealCategoryName, LucideIcon> = {
  [DealCategoryName.BIKES_AND_SCOOTERS]: Bike,
  [DealCategoryName.CLOTHING]: Shirt,
  [DealCategoryName.COSMETICS]: Droplets,
  [DealCategoryName.ELECTRONICS]: MonitorSmartphone,
  [DealCategoryName.FINANCE]: Euro,
  [DealCategoryName.CARS_AND_MOTORCYCLES]: Car,
  [DealCategoryName.FURNITURE]: Sofa,
  [DealCategoryName.GARDEN]: Tent,
  [DealCategoryName.GIFTS]: Gift,
  [DealCategoryName.HEALTH_AND_WELLNESS]: Heart,
  [DealCategoryName.HOME_AND_LIVING]: Building2,
  [DealCategoryName.HOME_IMPROVEMENT]: Hammer,
  [DealCategoryName.JEWELRY]: Gem,
  [DealCategoryName.KIDS]: Baby,
  [DealCategoryName.OTHER]: HelpCircle,
  [DealCategoryName.PETS]: Dog,
  [DealCategoryName.SPORTS]: Dumbbell,
  [DealCategoryName.TRAVEL]: Plane,
  [DealCategoryName.HOBBIES]: Guitar,
} as const;

export const isDealCategory = (
  category: string,
): category is DealCategoryName => {
  const categoryValues: Set<string> = new Set(Object.values(DealCategoryName));
  return categoryValues.has(category);
};

export const getDealCategoryIcon = (category: string): LucideIcon => {
  if (isDealCategory(category)) {
    return DEAL_CATEGORY_ICONS[category];
  }
  return Tag;
};

export const getDealCategoryTranslationKey = (
  category: DealCategoryName,
): string => {
  return LOCIZE_DEALS_KEYS.category[category]?.name || '';
};
