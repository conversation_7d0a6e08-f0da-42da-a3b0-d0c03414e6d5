import { Typography } from '@components/typography';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetDeals } from '@entities/deals/hooks/useGetDeals';
import { useIsMobileView } from '@hooks/system';
import { StarIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { DealsList } from '../DealsList';

const FeaturedDealsList = () => {
  const isMobileView = useIsMobileView();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const { data: deals } = useGetDeals({
    featured: true,
  });

  if (!deals?.length) {
    return null;
  }

  return (
    <div className="mx-6 mb-20 md:mx-12">
      <div className="flex items-center gap-3  pb-12 md:pb-8">
        <StarIcon className="h-6 w-6" />

        <Typography tag="h2" variant={isMobileView ? 'xxs' : 'xs'}>
          {t(LOCIZE_DEALS_KEYS.featuredDeals)}
        </Typography>
      </div>
      <DealsList data={deals} />
    </div>
  );
};

export default FeaturedDealsList;
