import { Typography } from '@components/typography';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useGetDeals } from '@entities/deals/hooks/useGetDeals';
import { cn } from '@utils/tailwind';
import { SearchX } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { DealsOrderBy, Direction } from '@/shared/types';

import { DealsList } from '../DealsList';

export const DealsNoResult = ({ className }: { className?: string }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const { data } = useGetDeals({
    orderBy: DealsOrderBy.CREATED_AT,
    direction: Direction.DESC,
    limit: 10,
  });

  if (!data?.length) {
    return null;
  }
  return (
    <div className={cn(className, 'flex h-full w-full flex-col')}>
      <div className="flex flex-col  justify-center rounded-lg bg-system-yellow3 p-4 mb-12">
        <div className="flex items-center gap-2">
          <SearchX className="size-5" />
          <Typography variant="text-m" affects="bold">
            {t(LOCIZE_DEALS_KEYS.noResults)}
          </Typography>
        </div>
      </div>
      <DealsList
        data={data}
        className="md:grid-cols-[repeat(auto-fill,minmax(230px,1fr))]"
        lgClassName="lg:grid-cols-[repeat(auto-fit,230px)]"
      />
    </div>
  );
};
