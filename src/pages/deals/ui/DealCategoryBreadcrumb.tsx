import { Typography } from '@components/typography';
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@components/ui/breadcrumb';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { Link } from '@tanstack/react-router';
import { ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { getDealsRouteApi } from '@/shared/utils/dealsRouteApi';

import { getDealCategoryTranslationKey, isDealCategory } from '../config';

type DealCategoryBreadcrumbProps = {
  className?: string;
};

const routeApi = getDealsRouteApi();

export const DealCategoryBreadcrumb = ({
  className,
}: DealCategoryBreadcrumbProps) => {
  const { title, category } = routeApi.useSearch();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const renderQueryBreadcrumb = () => {
    if (title) {
      return (
        <Typography
          variant="text-l"
          affects="medium"
          className="text-primary-black"
        >
          {`${t(LOCIZE_DEALS_KEYS.showingResultsFor)} ${title}`}
        </Typography>
      );
    }

    if (category && isDealCategory(category)) {
      return (
        <Typography
          variant="text-l"
          affects="bold"
          className="text-primary-black"
        >
          {t(getDealCategoryTranslationKey(category))}
        </Typography>
      );
    }

    return null;
  };

  const QueryBreadcrumb = renderQueryBreadcrumb();

  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link
              to="."
              replace
              search={(prev) => ({
                ...prev,
                title: undefined,
                category: undefined,
              })}
            >
              <Typography
                variant="text-l"
                affects="bold"
                className="text-primary-black"
              >
                {t(LOCIZE_DEALS_KEYS.dealsAll)}
              </Typography>
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator>
          <ChevronRight className="h-4 w-4" />
        </BreadcrumbSeparator>

        {QueryBreadcrumb && (
          <BreadcrumbItem>
            <BreadcrumbPage>{QueryBreadcrumb}</BreadcrumbPage>
          </BreadcrumbItem>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
};
