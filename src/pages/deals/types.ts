export type Deal = {
  id?: number;
  title?: string;
  description?: string;
  imagePath?: string | null;
  imageUrl?: string | null;
  merchantLogoPath?: string | null;
  merchantName?: string | null;
  categoryName?: string | null;
  discountLabel?: string | null;
  featured?: boolean;
};

export enum DealCategoryName {
  BIKES_AND_SCOOTERS = 'Bikes and scooters',
  CLOTHING = 'Clothing',
  COSMETICS = 'Cosmetics',
  ELECTRONICS = 'Electronics',
  FINANCE = 'Finance',
  CARS_AND_MOTORCYCLES = 'Cars and motorcycles',
  FURNITURE = 'Furniture',
  GARDEN = 'Garden',
  GIFTS = 'Gifts',
  HEALTH_AND_WELLNESS = 'Health & Wellness',
  HOME_AND_LIVING = 'Home & Living',
  HOME_IMPROVEMENT = 'Home Improvement',
  JEWELRY = 'Jewelry',
  KIDS = 'Kids',
  OTHER = 'Other',
  PETS = 'Pets',
  SPORTS = 'Sports',
  TRAVEL = 'Travel',
  HOBBIES = 'Hobbies',
}

export type DealCategory = Nullable<{ name: DealCategoryName }>;

export type NullableDealCategoryValue = Nullish<DealCategoryName>;
