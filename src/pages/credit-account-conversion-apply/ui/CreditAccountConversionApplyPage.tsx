import { Helmet } from '@components/Helmet';
import { LoanInfoCard } from '@components/loan-info-card/LoanInfoCard';
import { Typography } from '@components/typography';
import { Indicator } from '@components/ui/indicator';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS } from '@config/locize/credit-account-conversion';
import CreditAccountConversionSigningButton from '@features/credit-account-conversion';
import ArrowConversionIconLarge from '@icons/arrow-conversion-large.svg?react';
import ArrowConversionIconMedium from '@icons/arrow-conversion-medium.svg?react';
import ArrowConversionIconSmall from '@icons/arrow-conversion-small.svg?react';
import { useSearch } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

import { NonLoanProduct } from '@/shared/types';

import { useCreditAccountConvertionInfo } from '../hooks/useCreditAccountConvertionInfo';

export const CreditAccountConversionApplyPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.creditAccountConversion);

  const { referenceKey } = useSearch({ strict: false });

  if (!referenceKey)
    throw new Error('Application reference key is not found in url');

  const {
    reducedMonthlyPayment,
    creditAccountMonthlyPayment,
    applicationMonthlyPayment,
    merchantLogoSrc,
    productType,
    isCreditAccountActive,
  } = useCreditAccountConvertionInfo(referenceKey);

  return (
    <div className="grid place-items-center grid-cols-[1fr] grid-rows-[auto] max-w-[55rem] mx-auto">
      <Helmet title={t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.applyPageTitle)} />
      <div className="flex flex-col max-w-[60rem] w-full md:items-center">
        <div className="w-full">
          <div className="flex flex-col text-center mb-4">
            <Typography variant="m" tag="h1" className="mb-6 text-center">
              {t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.applyTitle)}
            </Typography>
            <Typography affects="semibold" className="text-center">
              {t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.applyDescription)}
            </Typography>
            <Typography
              variant="s"
              affects="bold"
              className="text-system-green"
            >
              {t(LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.reduceAmount, {
                reduceAmount: reducedMonthlyPayment,
              })}
            </Typography>
          </div>
          <div className="grid grid-rows-[1fr_0.8fr_1fr] [grid-template-areas:'creditLine'_'arrows'_'merchant'] w-full pb-[8rem] mb-6 mt-20 md:[grid-template-areas:'merchant_arrows_creditLine'] md:grid-rows-[auto] md:grid-cols-[1fr_0.3fr_1fr] md:py-6 md:mb-8 md:flex-row">
            <div className="[grid-area:merchant]">
              <LoanInfoCard
                title={t(
                  LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoTitle1,
                )}
                before={
                  <>
                    <Typography variant="m" affects="bold">
                      {t(
                        LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoAmount1,
                        {
                          productInfoAmount1: applicationMonthlyPayment,
                        },
                      )}
                    </Typography>
                    <Typography variant="text-s" affects="semibold">
                      {t(
                        LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoDescription1,
                      )}
                    </Typography>
                  </>
                }
                merchantLogoSrc={merchantLogoSrc}
                productType={productType}
                after={
                  <div className="flex gap-2 flex-col self-start mt-6">
                    <div className="flex text-start gap-1 md:items-center">
                      {' '}
                      <Indicator variant={'gray'} isAnimated={false} />
                      <Typography variant="text-s" affects="semibold">
                        {t(
                          LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoPoint1,
                        )}
                      </Typography>
                    </div>
                    <div className="flex text-start gap-1 md:items-center">
                      {' '}
                      <Indicator variant={'gray'} isAnimated={false} />
                      <Typography variant="text-s" affects="semibold">
                        {t(
                          LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoPoint2,
                        )}
                      </Typography>
                    </div>
                  </div>
                }
              />
            </div>
            <div className="[grid-area:arrows] flex items-center justify-center mb-12">
              <div className="flex gap-2 items-end md:rotate-90">
                <ArrowConversionIconSmall className="animate-bounce1" />
                <ArrowConversionIconLarge className="animate-bounce2" />
                <ArrowConversionIconMedium className="animate-bounce3" />
              </div>
            </div>
            <div className="[grid-area:creditLine]">
              <LoanInfoCard
                title={t(
                  LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoTitle2,
                )}
                before={
                  <>
                    <Typography
                      className="text-system-green"
                      variant="m"
                      affects="bold"
                    >
                      {t(
                        LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoAmount2,
                        {
                          productInfoAmount2: creditAccountMonthlyPayment,
                        },
                      )}
                    </Typography>
                    <Typography variant="text-s" affects="semibold">
                      {t(
                        LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoDescription2,
                      )}
                    </Typography>
                  </>
                }
                productType={NonLoanProduct.CREDIT_LINE}
                after={
                  <div className="flex gap-2 flex-col self-start mt-6">
                    <div className="flex text-start gap-1 md:items-center">
                      {' '}
                      <Indicator variant={'green'} isAnimated={true} />
                      <Typography variant="text-s" affects="semibold">
                        {t(
                          LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoPoint3,
                        )}
                      </Typography>
                    </div>
                    <div className="flex text-start gap-1 md:items-center">
                      {' '}
                      <Indicator variant={'green'} isAnimated={true} />
                      <Typography variant="text-s" affects="semibold">
                        {t(
                          LOCIZE_CREDIT_ACCOUNT_CONVERSION_KEYS.productInfoPoint4,
                        )}
                      </Typography>
                    </div>
                  </div>
                }
              />
            </div>
          </div>
        </div>
      </div>
      <div className="fixed z-10 bottom-0 left-0 right-0 w-full px-6 pt-8 pb-10 border-t border-neutral-200 bg-primary-white md:unset md:p-0 md:border-none">
        <CreditAccountConversionSigningButton
          isCreditAccountActive={isCreditAccountActive}
        />
      </div>
    </div>
  );
};
