import { ROUTE_NAMES } from '@config/routes';
import { createFileRoute, redirect } from '@tanstack/react-router';
import * as z from 'zod';

import { AppAuthMethod } from '@/shared/types';
import { getAndRemoveEParakstsOriginalUri } from '@/shared/utils/cookies';

const EPARAKSTS_CODE_SEARCH_NAME = 'code';

const eparakstsLoginSearchSchema = z.object({
  code: z.string().optional().catch(undefined),
});

export const Route = createFileRoute('/eparaksts-login')({
  beforeLoad: ({ search }) => {
    const originalUrl = getAndRemoveEParakstsOriginalUri();

    if (originalUrl) {
      let redirectUrl = originalUrl;

      if (search.code) {
        const url = new URL(originalUrl);
        url.searchParams.set(EPARAKSTS_CODE_SEARCH_NAME, search.code);
        redirectUrl = url.toString();
      }

      window.location.href = redirectUrl;
      return;
    }

    throw redirect({
      to: ROUTE_NAMES.auth,
      search: {
        authMethod: AppAuthMethod.EPARAKSTS_SMARTCARD,
      },
      replace: true,
    });
  },
  validateSearch: eparakstsLoginSearchSchema,
});
