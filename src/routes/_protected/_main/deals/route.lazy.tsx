import { ROUTE_NAMES } from '@config/routes';
import { DealsPage } from '@pages/deals/ui/DealsPage';
import {
  createLazyFileRoute,
  Outlet,
  useRouterState,
} from '@tanstack/react-router';

export const Route = createLazyFileRoute('/_protected/_main/deals')({
  component: Page,
});

function Page() {
  const { location } = useRouterState();
  const isExactDealsRoute = location.pathname === ROUTE_NAMES.deals;

  return isExactDealsRoute ? <DealsPage /> : <Outlet />;
}
