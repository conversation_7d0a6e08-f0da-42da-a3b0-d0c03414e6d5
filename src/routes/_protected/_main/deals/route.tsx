import { sidebarModel } from '@entities/sidebar';
import { DealCategoryName } from '@pages/deals/types';
import { createFileRoute, redirect } from '@tanstack/react-router';
import * as z from 'zod';

import { convertLegacyPathToLocalized } from '@/shared/utils/localizedRoutes';

const dealsSearchSchema = z.object({
  title: z.string().optional().catch(undefined),
  category: z.nativeEnum(DealCategoryName).optional().catch(undefined),
  showNewsletterPopup: z.boolean().optional().catch(undefined),
});

export const Route = createFileRoute('/_protected/_main/deals')({
  validateSearch: dealsSearchSchema,
  beforeLoad: ({ location }) => {
    // Redirect legacy /deals URLs to localized paths
    const localizedPath = convertLegacyPathToLocalized(location.pathname);
    if (localizedPath !== location.pathname) {
      throw redirect({
        to: localizedPath,
        search: location.search,
      });
    }
  },
  loader: () => {
    sidebarModel.events.setIsOpenEv(false);
  },
});
