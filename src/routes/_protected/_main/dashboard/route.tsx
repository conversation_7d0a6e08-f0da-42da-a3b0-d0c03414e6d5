import { ROUTE_NAMES } from '@config/routes';
import { createFileRoute, redirect } from '@tanstack/react-router';
import * as z from 'zod';

import {
  ConsumerLoanProduct,
  type EstoProductType,
  NonLoanProduct,
} from '@/shared/types';

const dashboardSearchSchema = z.object({
  hash: z.string().optional(),
  showNewsletterPopup: z.boolean().optional().catch(undefined),
  agreementReferenceKey: z.string().optional().catch(undefined),
  agreementPaymentConfirm: z.boolean().optional().catch(undefined),
  selectedLoanOffer: z
    .custom<EstoProductType>(
      (value) =>
        !value ||
        value in ConsumerLoanProduct ||
        value === NonLoanProduct.CREDIT_LINE,
    )
    .optional()
    .catch(undefined),
});

export const Route = createFileRoute('/_protected/_main/dashboard')({
  validateSearch: dashboardSearchSchema,
  beforeLoad: ({ search: { hash } }) => {
    if (hash) {
      throw redirect({
        replace: true,
        to: ROUTE_NAMES.creditAccountWithdrawal,
        search: {
          hash,
        },
      });
    }
  },
});
