import { sidebarModel } from '@entities/sidebar';
import { DealCategoryName } from '@pages/deals/types';
import { DealsPage } from '@pages/deals/ui/DealsPage';
import {
  createFileRoute,
  Outlet,
  useRouterState,
} from '@tanstack/react-router';
import * as z from 'zod';

const dealsSearchSchema = z.object({
  title: z.string().optional().catch(undefined),
  category: z.nativeEnum(DealCategoryName).optional().catch(undefined),
  showNewsletterPopup: z.boolean().optional().catch(undefined),
});

export const Route = createFileRoute('/_protected/_main/pakkumised')({
  validateSearch: dealsSearchSchema,
  loader: () => {
    sidebarModel.events.setIsOpenEv(false);
  },
  component: Page,
});

function Page() {
  const { location } = useRouterState();
  const isExactDealsRoute = location.pathname === '/pakkumised';

  return isExactDealsRoute ? <DealsPage /> : <Outlet />;
}
