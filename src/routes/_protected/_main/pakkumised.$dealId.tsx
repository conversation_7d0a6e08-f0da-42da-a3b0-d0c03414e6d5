import { PagePendingScreen } from '@components/pending-screens/PagePendingScreen';
import { DealPage } from '@pages/deal/DealPage';
import { DealCategoryName } from '@pages/deals/types';
import { createFileRoute } from '@tanstack/react-router';
import * as z from 'zod';

const dealParamsSchema = z.object({
  dealId: z.string().transform((val) => Number.parseInt(val, 10)),
});

const dealSearchSchema = z.object({
  category: z.nativeEnum(DealCategoryName).optional().catch(undefined),
});

export const Route = createFileRoute('/_protected/_main/pakkumised/$dealId')({
  validateSearch: dealSearchSchema,
  parseParams: dealParamsSchema.parse,
  component: DealPage,
  pendingComponent: PagePendingScreen,
});
