import { homepageMobileMenuModel } from '@entities/homepage/model';
import { useUnit } from 'effector-react';

import { BurgerButton } from '@/shared/components/BurgerButton';

export const HomePageBurgerButton = () => {
  const isOpenSidebar = useUnit(homepageMobileMenuModel.store.$isOpen);

  const handleButtonClick = () => {
    homepageMobileMenuModel.events.toggleEv();
  };

  return (
    <BurgerButton
      isOpen={isOpenSidebar}
      onClick={handleButtonClick}
      ariaLabel="Toggle homepage menu"
    />
  );
};
