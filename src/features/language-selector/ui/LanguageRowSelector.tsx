import { useLanguages } from '@entities/languages';
import { useZendeskWidget } from '@entities/support';
import { cn } from '@utils/tailwind';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

type LanguageRowSelectorProps = {
  className?: string;
};

export const LanguageRowSelector = ({
  className,
}: LanguageRowSelectorProps) => {
  const { i18n } = useTranslation();

  const { changeZendeskLocale } = useZendeskWidget();

  const handleLanguageChange = (lang: string) => async () => {
    await i18n.changeLanguage(lang);
    changeZendeskLocale(lang);
  };

  const languages = useLanguages();

  // Memoize filtered languages to avoid recalculation on every render
  const availableLanguages = useMemo(
    () => languages.filter((lang) => lang !== i18n.language),
    [languages, i18n.language],
  );

  return (
    <div className={cn('flex', className)}>
      {availableLanguages.map((lang) => (
        <button
          type="button"
          key={lang}
          className={cn(i18n.language === lang && 'font-bold', 'px-2.5')}
          onClick={handleLanguageChange(lang)}
        >
          {lang.toUpperCase()}
        </button>
      ))}
    </div>
  );
};
