import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';
import { HomepageHeader } from '@widgets/headers/homepage-header/ui/HomepageHeader';
import type { PropsWithChildren } from 'react';

export const HomepageLayout = ({ children }: PropsWithChildren) => {
  const isMobileView = useIsMobileView();

  return (
    <div className="bg-primary-white relative flex flex-col items-center h-full">
      <HomepageHeader />
      <main
        id="main-homepage-layout-scroll-container"
        className={cn(
          'pb-8 md:max-w-[1600px] size-full',
          isMobileView && 'pt-[4rem] overflow-y-auto no-scrollbar',
        )}
      >
        {children}
      </main>
    </div>
  );
};
