import { useIsMobileView } from '@hooks/system';

import { HomePageHeaderDesktop } from './HomePageHeaderDesktop';
import { HomePageHeaderMobile } from './HomePageHeaderMobile';

export const HomepageHeader = () => {
  const isMobileView = useIsMobileView();

  if (isMobileView) {
    return <HomePageHeaderMobile />;
  }

  return (
    <header className="w-full">
      <HomePageHeaderDesktop />
    </header>
  );
};
