import { Button } from '@components/ui/button';
import { APP_CONFIG } from '@config/app';
import { LOCIZE_AUTH_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { LanguageSelector } from '@features/language-selector';
import { useNavigate } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { useTranslation } from 'react-i18next';

import Logo from '@/shared/assets/logo-desktop.svg?react';

import {
  HOMEPAGE_DESKTOP_BOTTOM_HEADER_LINKS,
  HOMEPAGE_DESKTOP_TOP_HEADER_LINKS,
} from '../config';
import { useDealsNavigation } from '../hooks/useDealsNavigation';
import { HomepageHeaderLink } from './homepage-header-link';

export const HomePageHeaderDesktop = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);
  const { t: tN } = useTranslation(LOCIZE_NAMESPACES.navigation);
  const navigate = useNavigate();
  const { navigateToDeals } = useDealsNavigation();

  const onCtaClick = () => {
    navigate({
      to: ROUTE_NAMES.auth,
      search: {
        authMethod: APP_CONFIG.authMethods[0],
        redirectUrl: location.pathname,
      },
    });
  };

  return (
    <>
      <div className="w-full bg-primary-black flex flex-col items-center">
        <div className="flex justify-between items-center w-full md:max-w-[1600px] px-6 md:px-12">
          <div className="flex items-center -ml-4">
            {HOMEPAGE_DESKTOP_TOP_HEADER_LINKS.map(
              ({ title, isActive, ...link }) => (
                <HomepageHeaderLink
                  key={title}
                  className={cn(
                    'text-neutral-400 px-4 h-14 flex items-center hover:text-primary-white/85',
                    isActive && '!text-primary-white pointer-events-none',
                  )}
                  title={tN(title)}
                  affects="medium"
                  {...link}
                />
              ),
            )}
          </div>

          <LanguageSelector className="text-primary-white [&>p]:font-normal [&>svg]:size-[0.875rem]" />
        </div>
      </div>

      <div className="w-full flex flex-col items-center">
        <div className="flex justify-between items-center w-full md:max-w-[1600px] py-3 px-6 md:px-12">
          <Logo className="w-32 md:w-40" />

          <div className="flex items-center gap-2.5">
            {HOMEPAGE_DESKTOP_BOTTOM_HEADER_LINKS.map(
              ({ title, isActive, ...link }) => {
                // Special handling for deals link
                if (
                  'href' in link &&
                  typeof link.href === 'string' &&
                  (link.href === ROUTE_NAMES.deals ||
                    link.href === ROUTE_NAMES.dealsLocalized)
                ) {
                  return (
                    <HomepageHeaderLink
                      key={title}
                      className={cn(
                        'text-neutral-700 py-2 px-4 flex items-center hover:bg-neutral-50 hover:text-primary-black rounded-md',
                        '!text-primary-black transition-transform active:scale-[0.95]',
                      )}
                      affects={'semibold'}
                      title={tN(title)}
                      t={tN}
                      {...link}
                      onClick={navigateToDeals}
                    />
                  );
                }

                return (
                  <HomepageHeaderLink
                    key={title}
                    className={cn(
                      'text-neutral-700 py-2 px-4 flex items-center hover:bg-neutral-50 hover:text-primary-black rounded-md',
                      isActive && '!text-primary-black pointer-events-none',
                    )}
                    affects={isActive ? 'semibold' : 'normal'}
                    title={tN(title)}
                    t={tN}
                    {...link}
                  />
                );
              },
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button variant="grey" size="small" onClick={onCtaClick}>
              {t(LOCIZE_AUTH_KEYS.logIn)}
            </Button>
            <Button size="small" onClick={onCtaClick}>
              {t(LOCIZE_AUTH_KEYS.register)}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};
