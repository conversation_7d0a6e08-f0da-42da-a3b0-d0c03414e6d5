import { DefaultLink } from '@components/DefaultLink';
import { Typography } from '@components/typography';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@components/ui/navigation-menu';
import { cn } from '@utils/tailwind';
import type { TFunction } from 'i18next';

type ExpandableItemBase = {
  href: string;
  title: string;
  subtitle?: string;
};

type ExpandableMenuItemProps<T extends ExpandableItemBase> = {
  title: string;
  menuItems?: T[];
  className?: string;
  t?: TFunction;
  isOpenInNewTab?: boolean;
};

export const ExpandableMenuItem = <T extends ExpandableItemBase>({
  title,
  menuItems,
  className,
  isOpenInNewTab = false,
  t,
}: ExpandableMenuItemProps<T>) => {
  return (
    <div className={cn('flex justify-center', className)}>
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem>
            <NavigationMenuTrigger>
              <Typography className="font-normal text-inherit">
                {title}
              </Typography>
            </NavigationMenuTrigger>
            <NavigationMenuContent>
              <div className="grid w-[400px] gap-1 p-4">
                {menuItems?.map(({ href, title, subtitle }) => (
                  <NavigationMenuLink key={href} asChild>
                    <DefaultLink
                      href={href}
                      className="grid h-auto w-full items-center justify-start gap-1 rounded-md bg-background p-4 transition-colors hover:bg-accent hover:text-accent-foreground disabled:pointer-events-none disabled:opacity-50"
                      target={isOpenInNewTab ? '_blank' : undefined}
                    >
                      <div className="text-sm font-medium leading-none group-hover:underline">
                        {t ? t(title) : title}
                      </div>
                      {subtitle && (
                        <div className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          {t ? t(subtitle) : subtitle}
                        </div>
                      )}
                    </DefaultLink>
                  </NavigationMenuLink>
                ))}
              </div>
            </NavigationMenuContent>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
    </div>
  );
};
