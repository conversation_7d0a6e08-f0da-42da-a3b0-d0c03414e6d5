import { Button } from '@components/ui/button';
import { Separator } from '@components/ui/separator';
import { APP_CONFIG } from '@config/app';
import { LOCIZE_AUTH_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { homepageMobileMenuModel } from '@entities/homepage/model';
import { HomePageBurgerButton } from '@features/homepage-burger-button';
import { LanguageRowSelector } from '@features/language-selector/ui/LanguageRowSelector';
import { useNavigate } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';

import Logo from '@/shared/assets/logo-desktop.svg?react';

import {
  HOMEPAGE_MOBILE_BOTTOM_HEADER_LINKS,
  HOMEPAGE_MOBILE_TOP_HEADER_LINKS,
} from '../config';
import { HomepageHeaderLink } from './homepage-header-link';

export const HomePageHeaderMobile = () => {
  const isOpen = useUnit(homepageMobileMenuModel.store.$isOpen);

  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);
  const { t: tN } = useTranslation(LOCIZE_NAMESPACES.navigation);

  const navigate = useNavigate();

  const onCtaClick = () => {
    navigate({
      to: ROUTE_NAMES.auth,
      search: {
        authMethod: APP_CONFIG.authMethods[0],
        redirectUrl: location.pathname,
      },
    });
  };

  return (
    <div className="w-full fixed top-0 left-0 z-30">
      <div className="flex justify-between items-center bg-primary-white border-solid z-30 relative px-6 py-4 md:px-12">
        <Logo />
        <HomePageBurgerButton />
      </div>

      <Separator
        decorative
        className="text-neutral-200 md:opacity-0 relative z-30"
      />

      <aside
        className={cn(
          'fixed inset-0 h-[calc(100vh-3.5625rem)] -translate-y-[100%] bg-primary-white flex flex-col duration-300 ease-in-out overflow-y-auto no-scrollbar',
          isOpen && 'translate-y-[4rem]',
        )}
      >
        <div className="flex gap-4 px-6 py-4 border-solid">
          <Button variant="grey" onClick={onCtaClick}>
            {t(LOCIZE_AUTH_KEYS.logIn)}
          </Button>
          <Button onClick={onCtaClick}>{t(LOCIZE_AUTH_KEYS.register)}</Button>
        </div>

        <Separator
          decorative
          className="text-neutral-200 md:opacity-0 relative z-30"
        />

        <div className="flex flex-col gap-3 py-6">
          {HOMEPAGE_MOBILE_TOP_HEADER_LINKS.map(({ title, ...link }) => (
            <HomepageHeaderLink
              key={title}
              affects="bold"
              variant="text-l"
              className="px-6 h-[2.75rem] flex items-center hover:bg-neutral-50"
              title={tN(title)}
              t={tN}
              {...link}
            />
          ))}
        </div>

        <Separator
          decorative
          className="text-neutral-200 md:opacity-0 relative z-30"
        />

        <div className="flex flex-col gap-3 py-6">
          {HOMEPAGE_MOBILE_BOTTOM_HEADER_LINKS.map(({ title, ...link }) => (
            <HomepageHeaderLink
              key={title}
              affects="bold"
              variant="text-l"
              className="px-6 h-[2.75rem] flex items-center hover:bg-neutral-50"
              title={tN(title)}
              t={tN}
              {...link}
            />
          ))}
        </div>

        <LanguageRowSelector className="p-4" />
      </aside>
    </div>
  );
};
