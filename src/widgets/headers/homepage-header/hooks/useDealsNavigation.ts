import { ROUTE_NAMES } from '@config/routes';

import { getDealsRouteApi } from '@/shared/utils/dealsRouteApi';

const routeApi = getDealsRouteApi();

export const useDealsNavigation = () => {
  const navigate = routeApi.useNavigate();

  const navigateToDeals = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate({
      to: ROUTE_NAMES.dealsLocalized,
      search: undefined, // Clear all search params
      replace: true,
    });
  };

  return { navigateToDeals };
};
