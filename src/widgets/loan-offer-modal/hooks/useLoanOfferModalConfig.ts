import { LOCIZE_NAMESPACES, LOCIZE_OFFERS_KEYS } from '@config/locize';
import { useUserCreditAccount } from '@entities/user';
import { serializeCreditLineSettings } from '@utils/serializeCreditLineSettings';
import { loanOffersApi } from '@widgets/loan-offers/api';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  ConsumerLoanProduct,
  type EstoProductType,
  NonLoanProduct,
} from '@/shared/types';

import type { LoanOfferModalConfig } from '../types';

export const useLoanOfferModalConfig = (productType: EstoProductType) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.offers);
  const { data } = loanOffersApi.useSuspenseLoanOffersSettingsQuery();
  const { data: creditAccount } = useUserCreditAccount();

  const {
    max_loan_amount: maxSmallLoanAmount,
    possible_periods: possibleSmallLoanPeriods,
  } = data?.APPLICATION?.SMALL_LOAN || {};

  const {
    max_loan_amount: maxFastLoanAmount,
    possible_periods: possibleFastLoanPeriods,
  } = data?.APPLICATION?.FAST_LOAN || {};

  const {
    max_loan_amount: maxRenovationLoanAmount,
    possible_periods: possibleRenovationLoanPeriods,
  } = data?.APPLICATION?.RENOVATION_LOAN || {};

  const {
    max_loan_amount: maxVehicleLoanAmount,
    possible_periods: possibleVehicleLoanPeriods,
  } = data?.APPLICATION?.VEHICLE_LOAN || {};

  const {
    max_loan_amount: maxTravelLoanAmount,
    possible_periods: possibleTravelLoanPeriods,
  } = data?.APPLICATION?.TRAVEL_LOAN || {};

  const {
    max_loan_amount: maxHealthLoanAmount,
    possible_periods: possibleHealthLoanPeriods,
  } = data?.APPLICATION?.HEALTH_LOAN || {};

  const {
    max_loan_amount: maxBeautyLoanAmount,
    possible_periods: possibleBeautyLoanPeriods,
  } = data?.APPLICATION?.BEAUTY_LOAN || {};

  const config = useMemo((): LoanOfferModalConfig => {
    const baseConfigs = {
      [ConsumerLoanProduct.FAST_LOAN]: {
        title: LOCIZE_OFFERS_KEYS.fastLoanTitle,
        description: LOCIZE_OFFERS_KEYS.modalFastLoanDescription1,
        imgSrc: '/images/dashboard/products/fast-loan-v2.webp',
        features: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: maxFastLoanAmount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possibleFastLoanPeriods?.at(-1) ?? 0,
          }),
        ],
      },
      [ConsumerLoanProduct.SMALL_LOAN]: {
        title: LOCIZE_OFFERS_KEYS.smallLoanTitle,
        description: LOCIZE_OFFERS_KEYS.modalSmallLoanDescription1,
        imgSrc: '/images/dashboard/products/small-loan-v2.webp',
        features: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: maxSmallLoanAmount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possibleSmallLoanPeriods?.at(-1) ?? 0,
          }),
        ],
      },
      [ConsumerLoanProduct.RENOVATION_LOAN]: {
        title: LOCIZE_OFFERS_KEYS.renovationLoanTitle,
        description: LOCIZE_OFFERS_KEYS.modalRenovationLoanDescription1,
        imgSrc: '/images/dashboard/products/renovation-loan-v2.webp',
        features: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: maxRenovationLoanAmount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possibleRenovationLoanPeriods?.at(-1) ?? 0,
          }),
        ],
      },
      [ConsumerLoanProduct.VEHICLE_LOAN]: {
        title: LOCIZE_OFFERS_KEYS.vehicleLoanTitle,
        description: LOCIZE_OFFERS_KEYS.modalVehicleLoanDescription1,
        imgSrc: '/images/dashboard/products/vehicle-loan-v2.webp',
        features: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: maxVehicleLoanAmount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possibleVehicleLoanPeriods?.at(-1) ?? 0,
          }),
        ],
      },
      [NonLoanProduct.CREDIT_LINE]: {
        title: LOCIZE_OFFERS_KEYS.creditLineTitle,
        description: LOCIZE_OFFERS_KEYS.modalCreditLineDescription1,
        imgSrc: '/images/dashboard/products/credit-line-v2.webp',
        features: [
          t(LOCIZE_OFFERS_KEYS.creditLineOfferDescription1, {
            amount:
              data?.CREDIT_LINE && !creditAccount?.isActive
                ? serializeCreditLineSettings(data.CREDIT_LINE).maxAmount
                : 0,
          }),
          t(LOCIZE_OFFERS_KEYS.creditLineOfferDescription2),
          t(LOCIZE_OFFERS_KEYS.creditLineOfferDescription3),
        ],
      },

      [ConsumerLoanProduct.TRAVEL_LOAN]: {
        title: LOCIZE_OFFERS_KEYS.travelLoanTitle,
        description: LOCIZE_OFFERS_KEYS.modalTravelLoanDescription1,
        imgSrc: '/images/dashboard/products/travel-loan.webp',
        features: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: maxTravelLoanAmount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possibleTravelLoanPeriods?.at(-1) ?? 0,
          }),
        ],
      },
      [ConsumerLoanProduct.HEALTH_LOAN]: {
        title: LOCIZE_OFFERS_KEYS.healthLoanTitle,
        description: LOCIZE_OFFERS_KEYS.modalHealthLoanDescription1,
        imgSrc: '/images/dashboard/products/health-loan.webp',
        features: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: maxHealthLoanAmount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possibleHealthLoanPeriods?.at(-1) ?? 0,
          }),
        ],
      },
      [ConsumerLoanProduct.BEAUTY_LOAN]: {
        title: LOCIZE_OFFERS_KEYS.beautyLoanTitle,
        description: LOCIZE_OFFERS_KEYS.modalBeautyLoanDescription1,
        imgSrc: '/images/dashboard/products/beauty-loan.webp',
        features: [
          t(LOCIZE_OFFERS_KEYS.descriptionBorrowUpTo, {
            amount: maxBeautyLoanAmount,
          }),
          t(LOCIZE_OFFERS_KEYS.descriptionPeriodUpTo, {
            period: possibleBeautyLoanPeriods?.at(-1) ?? 0,
          }),
        ],
      },
    };

    return baseConfigs[productType];
  }, [productType, data, t]);

  return config;
};
