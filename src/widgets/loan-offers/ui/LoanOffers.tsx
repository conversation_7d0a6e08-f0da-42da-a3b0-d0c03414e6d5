import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES, LOCIZE_OFFERS_KEYS } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { OfferCard } from '@entities/product/offer';
import { useAppConfig, useIsMobileView } from '@hooks/system';
import { Link, useNavigate, useSearch } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import c from 'clsx';
import { type FC, lazy, Suspense, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { NonLoanProduct } from '@/shared/types';

import { CardsCarousel } from '../../../shared/components/ui/CardsCarousel';
import { useLoanOffers } from '../hooks';

const LoanOfferModal = lazy(() =>
  import('@widgets/loan-offer-modal').then((module) => ({
    default: module.LoanOfferModal,
  })),
);

type LoanOffersProps = {
  className?: string;
};

export const LoanOffers: FC<LoanOffersProps> = ({ className }) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.offers);
  const { starProduct } = useAppConfig();
  const { offers } = useLoanOffers();
  const { selectedLoanOffer, ...currentSearch } = useSearch({
    strict: false,
  });
  const navigate = useNavigate();
  const isMobileView = useIsMobileView();

  const handleModalClose = useCallback(() => {
    navigate({
      to: ROUTE_NAMES.current,
      replace: true,
    });
  }, [navigate]);

  const cardWidths = useMemo(
    () => ({
      creditLine: isMobileView ? 'basis-[16.625rem]' : 'basis-[26.25rem]',
      default: 'basis-[16.625rem]',
    }),
    [isMobileView],
  );

  if (!offers?.length) {
    return null;
  }

  return (
    <>
      <div className={c('grid gap-1 pb-7 md:p-0 w-full', className)}>
        <CardsCarousel
          title={
            <Typography tag="h2" variant="xs" className="px-6 md:px-0">
              {t(LOCIZE_OFFERS_KEYS.blockTitle)}
            </Typography>
          }
        >
          {offers?.map((offer) => (
            <div
              key={offer.productType}
              className={cn(
                'flex-shrink-0 pl-6',
                offer.productType === NonLoanProduct.CREDIT_LINE
                  ? cardWidths.creditLine
                  : cardWidths.default,
              )}
            >
              <Link
                to={ROUTE_NAMES.current}
                search={{
                  ...currentSearch,
                  selectedLoanOffer: offer.productType,
                }}
                resetScroll={false}
                className="block"
              >
                <OfferCard
                  isStarProduct={offer.productType === starProduct}
                  className={cn('transition-shadow')}
                  {...offer}
                />
              </Link>
            </div>
          ))}
        </CardsCarousel>
      </div>

      <Suspense>
        {selectedLoanOffer && (
          <LoanOfferModal
            open={!!selectedLoanOffer}
            onOpenChange={(state: boolean) => {
              if (!state) {
                handleModalClose();
              }
            }}
            productType={selectedLoanOffer}
            isStarProduct={selectedLoanOffer === starProduct}
          />
        )}
      </Suspense>
    </>
  );
};
