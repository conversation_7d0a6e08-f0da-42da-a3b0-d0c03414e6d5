import { useMemo } from 'react';

import { pricingApi } from '../api';
import { PRICING_KEYS } from '../config';

export const useExampleCreditLimitPricingConfig = () => {
  const { data } = pricingApi.useSuspensePricingQuery({
    keys: [PRICING_KEYS.exampleDefaultCreditLimitAmount],
  });

  return useMemo(() => {
    return (
      data?.pricing?.reduce<{
        exampleDefaultCreditLimitAmount: number;
      }>(
        (acc, p) => {
          if (!p) {
            return acc;
          }
          if (p.key === PRICING_KEYS.exampleDefaultCreditLimitAmount) {
            acc.exampleDefaultCreditLimitAmount = +p.value;
          }

          return acc;
        },
        {
          exampleDefaultCreditLimitAmount: 0,
        },
      ) ?? {
        exampleDefaultCreditLimitAmount: 0,
      }
    );
  }, [data?.pricing]);
};
