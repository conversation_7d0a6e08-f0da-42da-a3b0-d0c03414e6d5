import { LANGUAGE_ABBREVIATION_BY_SHORTNAME } from '@entities/languages';
import { fetcher } from '@lib/fetcher';
import type { Deal } from '@pages/deals/types';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { DealsOrderBy, DealStatus, Direction } from '@/shared/types';

import {
  DealsDocument,
  type DealsQuery,
  type DealsQueryVariables,
} from '../api/queries.gen';

export interface UseInfiniteDealsOptions {
  variables?: Omit<DealsQueryVariables, 'page' | 'limit'>;
  limit?: number;
  enabled?: boolean;
}

export interface UseInfiniteDealsResult {
  data: Deal[];
  isLoading: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  error: Error | null;
  fetchNextPage: () => void;
  currentPage: number;
  total: number;
}

export const useInfiniteDeals = (
  options: UseInfiniteDealsOptions = {},
): UseInfiniteDealsResult => {
  const { variables = {}, limit = 20, enabled = true } = options;
  const { i18n } = useTranslation();

  const transformDeal = (
    deal: NonNullable<NonNullable<DealsQuery['deals']>['data']>[number],
  ): Deal | null => {
    if (!deal) return null;

    const translations = deal.translations?.find(
      (translation: NonNullable<typeof deal.translations>[number]) =>
        translation?.language ===
        LANGUAGE_ABBREVIATION_BY_SHORTNAME[i18n.language],
    );
    const dealTitle = translations?.deal_title;
    const dealDescription = translations?.description;

    if (!deal.id || !dealTitle) return null;

    return {
      id: deal.id,
      title: dealTitle,
      imageUrl: deal.image_url,
      imagePath: deal.image_path,
      categoryName: deal.category_name,
      description: dealDescription,
      merchantName: deal.merchant?.name,
      merchantLogoPath: deal.merchant?.deals_logo_url,
      featured: deal.featured,
      discountLabel: translations?.discount_label,
    };
  };

  const baseVariables = useMemo(
    () => ({
      ...variables,
      statuses: [DealStatus.ACTIVE],
      orderBy: DealsOrderBy.CREATED_AT,
      direction: Direction.DESC,
      limit,
    }),
    [variables, limit],
  );

  const {
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    error,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: ['InfiniteDeals', baseVariables],
    queryFn: ({ pageParam = 1 }) =>
      fetcher<DealsQuery, DealsQueryVariables>(DealsDocument, {
        ...baseVariables,
        page: pageParam,
      })(),
    getNextPageParam: (lastPage) => {
      const dealsData = lastPage?.deals;
      if (!dealsData?.has_more_pages) return undefined;
      return (dealsData.current_page || 0) + 1;
    },
    enabled,
    initialPageParam: 1,
  });

  const transformedData = useMemo(() => {
    if (!data?.pages) return [];

    return data.pages
      .flatMap((page) => page?.deals?.data || [])
      .map(transformDeal)
      .filter((deal): deal is Deal => deal !== null);
  }, [data?.pages, i18n.language]);

  const lastPage = data?.pages?.[data.pages.length - 1];
  const currentPage = lastPage?.deals?.current_page || 1;
  const total = lastPage?.deals?.total || 0;

  return {
    data: transformedData,
    isLoading,
    isFetchingNextPage,
    hasNextPage: hasNextPage || false,
    error: error || null,
    fetchNextPage: () => void fetchNextPage(),
    currentPage,
    total,
  };
};
