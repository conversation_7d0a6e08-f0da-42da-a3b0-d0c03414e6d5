import { fetcher } from '@lib/fetcher';
import {
  type InfiniteData,
  useInfiniteQuery,
  type UseInfiniteQueryOptions,
} from '@tanstack/react-query';
import { useMemo } from 'react';

export interface DealCategory {
  name: string;
}

export interface DealCategoryPagination {
  data: DealCategory[];
  total: number;
  per_page: number;
  current_page: number;
  from: number | null;
  to: number | null;
  last_page: number;
  has_more_pages: boolean;
}

export interface DealsCategoriesQueryResponse {
  deals_categories: DealCategoryPagination;
}

export interface DealsCategoriesQueryVariables {
  limit?: number;
  page?: number;
  only_with_deals?: boolean;
}

export interface UseInfiniteCategoriesOptions {
  only_with_deals?: boolean;
  limit?: number;
  enabled?: boolean;
}

export interface UseInfiniteCategoriesResult {
  data: DealCategory[];
  isLoading: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  error: Error | null;
  fetchNextPage: () => void;
  total: number;
}

const DealsCategoriesPaginatedDocument = `
  query DealsCategoriesPaginated($limit: Int, $page: Int, $only_with_deals: Boolean) {
    deals_categories(limit: $limit, page: $page, only_with_deals: $only_with_deals) {
      data {
        name
      }
      total
      per_page
      current_page
      from
      to
      last_page
      has_more_pages
    }
  }
`;

export const useInfiniteCategoriesQuery = <
  TData = InfiniteData<DealsCategoriesQueryResponse>,
  TError = unknown,
>(
  variables: Omit<DealsCategoriesQueryVariables, 'page'>,
  options: Omit<
    UseInfiniteQueryOptions<DealsCategoriesQueryResponse, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseInfiniteQueryOptions<
      DealsCategoriesQueryResponse,
      TError,
      TData
    >['queryKey'];
  },
) => {
  return useInfiniteQuery<DealsCategoriesQueryResponse, TError, TData>(
    (() => {
      const { queryKey: optionsQueryKey, ...restOptions } = options;
      return {
        queryKey: optionsQueryKey ?? [
          'DealsCategoriesPaginated.infinite',
          variables,
        ],
        queryFn: (metaData) =>
          fetcher<DealsCategoriesQueryResponse, DealsCategoriesQueryVariables>(
            DealsCategoriesPaginatedDocument,
            {
              ...variables,
              ...(metaData.pageParam ?? {}),
            },
          )(),
        ...restOptions,
      };
    })(),
  );
};

export const useInfiniteCategories = (
  options: UseInfiniteCategoriesOptions = {},
): UseInfiniteCategoriesResult => {
  const { only_with_deals = true, limit = 3, enabled = true } = options;

  const {
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    error,
  } = useInfiniteCategoriesQuery(
    {
      limit,
      only_with_deals,
    },
    {
      enabled,
      initialPageParam: {
        page: 1,
      },
      getNextPageParam: (lastPage, _, lastPageParam) =>
        lastPage?.deals_categories?.has_more_pages
          ? {
              page: (lastPageParam as { page: number }).page + 1,
            }
          : undefined,
    },
  );

  const allCategories = useMemo(() => {
    if (!data?.pages) return [];
    return data.pages.flatMap((page) => page.deals_categories?.data || []);
  }, [data?.pages]);

  const total = data?.pages?.[0]?.deals_categories?.total ?? 0;

  return {
    data: allCategories,
    isLoading,
    isFetchingNextPage,
    hasNextPage: hasNextPage ?? false,
    error: error as Error | null,
    fetchNextPage: () => {
      fetchNextPage();
    },
    total,
  };
};
