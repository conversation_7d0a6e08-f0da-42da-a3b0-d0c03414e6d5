name: 'Install & Cache Dependencies'
description: 'Install dependencies and cache them for future runs'
inputs:
  node-version:
    description: 'The Node.js version to use'
    required: false
    default: '20'
  pnpm-version:
    description: 'The pnpm version to use'
    required: false
    default: '9'
runs:
  using: composite
  steps:
    - uses: pnpm/action-setup@v3
      with:
        version: ${{ inputs.pnpm-version }}
    - name: Use Node.js ${{ inputs.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'pnpm'
    - name: Install dependencies
      shell: bash
      run: pnpm install
